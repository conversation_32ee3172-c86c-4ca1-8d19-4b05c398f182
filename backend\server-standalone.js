// Standalone server with minimal dependencies for shared hosting
const http = require('http');
const url = require('url');
const querystring = require('querystring');

// Simple CORS handler
function setCORSHeaders(res) {
  res.setHeader('Access-Control-Allow-Origin', '*');
  res.setHeader('Access-Control-Allow-Methods', 'GET, POST, PUT, DELETE, OPTIONS');
  res.setHeader('Access-Control-Allow-Headers', 'Content-Type, Authorization');
}

// Simple JSON response helper
function sendJSON(res, statusCode, data) {
  setCORSHeaders(res);
  res.writeHead(statusCode, { 'Content-Type': 'application/json' });
  res.end(JSON.stringify(data));
}

// Mock data for packages (replace with database when available)
const packages = [
  {
    _id: '1',
    name: 'Basic',
    description: 'Perfect for beginners starting their trading journey',
    price: 29,
    duration: 30,
    features: [
      'Access to basic courses',
      'Community forum access',
      'Email support',
      'Basic trading guides',
      'Mobile app access'
    ],
    isActive: true,
    popular: false,
    color: 'gray',
    displayOrder: 1,
    subscribers: 156
  },
  {
    _id: '2',
    name: 'Premium',
    description: 'Most popular choice for serious traders',
    price: 99,
    duration: 30,
    features: [
      'Access to all courses',
      'Premium Telegram signals',
      '1-on-1 mentoring sessions',
      'Advanced trading strategies',
      'Priority support',
      'Market analysis reports',
      'Live trading sessions',
      'Risk management tools'
    ],
    isActive: true,
    popular: true,
    color: 'blue',
    displayOrder: 2,
    subscribers: 642
  },
  {
    _id: '3',
    name: 'VIP',
    description: 'For professional traders seeking maximum results',
    price: 199,
    duration: 30,
    features: [
      'Everything in Premium',
      'Personal trading coach',
      'Custom trading strategies',
      'Direct access to expert traders',
      'Weekly live sessions',
      'Portfolio review',
      'Exclusive market insights',
      'Priority Telegram channel'
    ],
    isActive: true,
    popular: false,
    color: 'purple',
    displayOrder: 3,
    subscribers: 94
  }
];

// Simple request handler
function handleRequest(req, res) {
  const parsedUrl = url.parse(req.url, true);
  const path = parsedUrl.pathname;
  const method = req.method;

  // Handle CORS preflight
  if (method === 'OPTIONS') {
    setCORSHeaders(res);
    res.writeHead(200);
    res.end();
    return;
  }

  // Health check
  if (path === '/api/health') {
    sendJSON(res, 200, {
      success: true,
      message: 'ForexClass API is running',
      timestamp: new Date().toISOString(),
      version: '1.0.0'
    });
    return;
  }

  // Get packages
  if (path === '/api/packages' && method === 'GET') {
    const activePackages = packages.filter(pkg => pkg.isActive);
    sendJSON(res, 200, {
      success: true,
      data: {
        packages: activePackages
      }
    });
    return;
  }

  // Get packages for admin
  if (path === '/api/packages/admin' && method === 'GET') {
    sendJSON(res, 200, {
      success: true,
      data: {
        packages: packages
      }
    });
    return;
  }

  // Simple auth endpoint (mock)
  if (path === '/api/auth/login' && method === 'POST') {
    let body = '';
    req.on('data', chunk => {
      body += chunk.toString();
    });
    
    req.on('end', () => {
      try {
        const { email, password } = JSON.parse(body);
        
        // Mock admin login
        if (email === '<EMAIL>' && password === 'admin123') {
          sendJSON(res, 200, {
            success: true,
            message: 'Login successful',
            data: {
              user: {
                id: '1',
                email: '<EMAIL>',
                role: 'admin',
                name: 'Admin User'
              },
              token: 'mock-jwt-token-' + Date.now()
            }
          });
        } else {
          sendJSON(res, 401, {
            success: false,
            message: 'Invalid credentials'
          });
        }
      } catch (error) {
        sendJSON(res, 400, {
          success: false,
          message: 'Invalid JSON'
        });
      }
    });
    return;
  }

  // Default 404
  sendJSON(res, 404, {
    success: false,
    message: 'Endpoint not found'
  });
}

// Create and start server
const PORT = process.env.PORT || 3000;
const server = http.createServer(handleRequest);

server.listen(PORT, () => {
  console.log(`🚀 ForexClass API Server running on port ${PORT}`);
  console.log(`📍 Health check: http://localhost:${PORT}/api/health`);
  console.log(`📦 Packages: http://localhost:${PORT}/api/packages`);
  console.log(`🔐 Admin login: <EMAIL> / admin123`);
});

// Handle server errors
server.on('error', (error) => {
  console.error('❌ Server error:', error);
});

// Graceful shutdown
process.on('SIGTERM', () => {
  console.log('🛑 Received SIGTERM, shutting down gracefully');
  server.close(() => {
    console.log('✅ Server closed');
    process.exit(0);
  });
});
