const mongoose = require('mongoose');

const packageSchema = new mongoose.Schema({
  name: {
    type: String,
    required: [true, 'Package name is required'],
    trim: true,
    maxlength: [50, 'Package name cannot exceed 50 characters']
  },
  description: {
    type: String,
    trim: true,
    maxlength: [200, 'Description cannot exceed 200 characters']
  },
  price: {
    type: Number,
    required: [true, 'Price is required'],
    min: [0, 'Price cannot be negative']
  },
  duration: {
    type: Number,
    required: [true, 'Duration is required'],
    min: [1, 'Duration must be at least 1 day'],
    default: 30
  },
  features: [{
    type: String,
    trim: true,
    maxlength: [100, 'Feature description cannot exceed 100 characters']
  }],
  isActive: {
    type: Boolean,
    default: true
  },
  popular: {
    type: Boolean,
    default: false
  },
  color: {
    type: String,
    enum: ['blue', 'purple', 'green', 'red', 'gray', 'indigo'],
    default: 'blue'
  },
  displayOrder: {
    type: Number,
    default: 0
  },
  subscribers: {
    type: Number,
    default: 0
  }
}, {
  timestamps: true
});

// Index for efficient querying
packageSchema.index({ isActive: 1, displayOrder: 1 });

// Virtual for formatted price
packageSchema.virtual('formattedPrice').get(function() {
  return `$${this.price}`;
});

// Static method to get active packages
packageSchema.statics.getActivePackages = function() {
  return this.find({ isActive: true }).sort({ displayOrder: 1, createdAt: 1 });
};

// Static method to get all packages for admin
packageSchema.statics.getAllPackagesForAdmin = function() {
  return this.find().sort({ displayOrder: 1, createdAt: 1 });
};

// Method to update subscriber count
packageSchema.methods.updateSubscriberCount = async function() {
  const User = mongoose.model('User');
  const count = await User.countDocuments({ 'subscription.plan': this.name });
  this.subscribers = count;
  return this.save();
};

module.exports = mongoose.model('Package', packageSchema);
