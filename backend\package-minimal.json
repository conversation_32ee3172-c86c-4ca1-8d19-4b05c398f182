{"name": "forexclass-backend", "version": "1.0.0", "description": "ForexClass Trading Platform Backend API", "main": "server.js", "scripts": {"start": "node server.js"}, "dependencies": {"express": "4.18.2", "mongoose": "8.0.3", "cors": "2.8.5", "helmet": "7.1.0", "bcryptjs": "2.4.3", "jsonwebtoken": "9.0.2", "express-validator": "7.0.1", "express-rate-limit": "7.1.5", "dotenv": "16.3.1"}, "engines": {"node": ">=16.0.0"}}