#!/bin/bash

# ForexClass Deployment Script
echo "🚀 Starting ForexClass Backend Deployment..."

# Set Node.js version (adjust as needed)
export NODE_VERSION=18

# Create necessary directories
mkdir -p logs
mkdir -p tmp

# Copy production package.json
echo "📦 Setting up production dependencies..."
cp backend/package-production.json backend/package.json

# Install dependencies with reduced memory usage
echo "⬇️ Installing dependencies..."
cd backend
npm install --production --no-optional --no-audit --no-fund --maxsockets 1

# Copy environment file if it doesn't exist
if [ ! -f .env ]; then
    echo "⚙️ Creating environment file..."
    cat > .env << EOL
NODE_ENV=production
PORT=3000
MONGODB_URI=mongodb://localhost:27017/forexclass
JWT_SECRET=your-super-secret-jwt-key-change-this-in-production
JWT_EXPIRES_IN=7d
FRONTEND_URL=https://test.quatromgt.co.ke
EOL
fi

echo "✅ Backend deployment completed!"
echo "🔧 To start the server, run: node server.js"
echo "📝 Make sure to update the .env file with your actual values"
