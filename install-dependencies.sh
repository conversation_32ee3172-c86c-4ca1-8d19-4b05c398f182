#!/bin/bash

# Manual dependency installation script for shared hosting
# Use this when npm fails with "fork: Resource temporarily unavailable"

echo "🔧 Installing ForexClass Backend Dependencies Manually..."

# Set memory limits
export NODE_OPTIONS="--max-old-space-size=256"

# Install core dependencies one by one with minimal resource usage
echo "📦 Installing Express..."
npm install express@^4.18.2 --no-optional --no-audit --no-fund --maxsockets 1 --timeout 60000

echo "📦 Installing Mongoose..."
npm install mongoose@^8.0.3 --no-optional --no-audit --no-fund --maxsockets 1 --timeout 60000

echo "📦 Installing CORS..."
npm install cors@^2.8.5 --no-optional --no-audit --no-fund --maxsockets 1 --timeout 60000

echo "📦 Installing Helmet..."
npm install helmet@^7.1.0 --no-optional --no-audit --no-fund --maxsockets 1 --timeout 60000

echo "📦 Installing bcryptjs..."
npm install bcryptjs@^2.4.3 --no-optional --no-audit --no-fund --maxsockets 1 --timeout 60000

echo "📦 Installing jsonwebtoken..."
npm install jsonwebtoken@^9.0.2 --no-optional --no-audit --no-fund --maxsockets 1 --timeout 60000

echo "📦 Installing express-validator..."
npm install express-validator@^7.0.1 --no-optional --no-audit --no-fund --maxsockets 1 --timeout 60000

echo "📦 Installing express-rate-limit..."
npm install express-rate-limit@^7.1.5 --no-optional --no-audit --no-fund --maxsockets 1 --timeout 60000

echo "📦 Installing dotenv..."
npm install dotenv@^16.3.1 --no-optional --no-audit --no-fund --maxsockets 1 --timeout 60000

echo "✅ All dependencies installed successfully!"
echo "🚀 You can now start the server with: node server.js"
