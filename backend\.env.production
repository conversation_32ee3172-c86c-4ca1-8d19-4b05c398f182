# Production Environment Variables
# Copy this file to .env and update with your actual values

NODE_ENV=production
PORT=3000

# MongoDB Connection
# Option 1: MongoDB Atlas (Recommended)
MONGODB_URI=mongodb+srv://username:<EMAIL>/forexclass?retryWrites=true&w=majority

# Option 2: Local MongoDB
# MONGODB_URI=mongodb://localhost:27017/forexclass

# JWT Configuration
JWT_SECRET=your-super-secret-jwt-key-minimum-32-characters-long-change-this-in-production
JWT_EXPIRES_IN=7d

# Frontend URL (Update with your domain)
FRONTEND_URL=https://test.quatromgt.co.ke

# CORS Origins (Update with your domain)
CORS_ORIGIN=https://test.quatromgt.co.ke

# Rate Limiting
RATE_LIMIT_WINDOW_MS=900000
RATE_LIMIT_MAX_REQUESTS=100

# Security
BCRYPT_ROUNDS=12
