# ForexClass Deployment Guide

## ✅ Build Status
- **Frontend**: ✅ Built successfully (dist folder ready)
- **Backend**: ✅ Production package.json created
- **Database**: ✅ Seeded with packages

## 🚨 Shared Hosting Deployment (cPanel/Similar)

### Prerequisites
- Node.js app support enabled
- MongoDB access (local or cloud)
- File manager access

### Step 1: Prepare Files for Upload

1. **Backend Files to Upload:**
   ```
   backend/
   ├── server.js
   ├── package-production.json (rename to package.json)
   ├── start-production.js
   ├── models/
   ├── routes/
   ├── middleware/
   └── .env (create manually)
   ```

2. **Frontend Files to Upload:**
   ```
   frontend/dist/ (after running npm run build)
   ```

### Step 2: Manual Installation (If npm fails)

If you get "fork: Resource temporarily unavailable" errors:

1. **Upload files via File Manager**
2. **Create .env file manually:**
   ```
   NODE_ENV=production
   PORT=3000
   MONGODB_URI=your_mongodb_connection_string
   JWT_SECRET=your-super-secret-jwt-key-change-this-in-production
   JWT_EXPIRES_IN=7d
   FRONTEND_URL=https://test.quatromgt.co.ke
   ```

3. **Install dependencies one by one:**
   ```bash
   npm install express --no-optional
   npm install mongoose --no-optional
   npm install cors --no-optional
   npm install helmet --no-optional
   npm install bcryptjs --no-optional
   npm install jsonwebtoken --no-optional
   npm install express-validator --no-optional
   npm install express-rate-limit --no-optional
   npm install dotenv --no-optional
   ```

### Step 3: Alternative - Use Yarn (Often works better on shared hosting)

```bash
yarn install --production --no-optional
```

### Step 4: Start the Application

1. **Using Node.js App in cPanel:**
   - Set startup file to: `start-production.js`
   - Set Node.js version to 18 or latest available

2. **Manual start:**
   ```bash
   node start-production.js
   ```

### Step 5: Frontend Build and Deploy

1. **Build frontend locally:**
   ```bash
   cd frontend
   npm run build
   ```

2. **Upload dist/ folder contents to public_html**

### Troubleshooting

#### Error: "fork: Resource temporarily unavailable"
- **Solution 1:** Use yarn instead of npm
- **Solution 2:** Install dependencies one by one
- **Solution 3:** Contact hosting provider to increase process limits

#### Error: "No such file or directory"
- **Solution:** Ensure all file paths are correct
- **Solution:** Check file permissions (755 for directories, 644 for files)

#### Error: "Read-only file system"
- **Solution:** Don't try to create symlinks
- **Solution:** Upload files directly to the correct directories

### Environment Variables for Production

```env
NODE_ENV=production
PORT=3000
MONGODB_URI=mongodb+srv://username:<EMAIL>/forexclass
JWT_SECRET=your-super-secret-jwt-key-minimum-32-characters-long
JWT_EXPIRES_IN=7d
FRONTEND_URL=https://test.quatromgt.co.ke
```

### Database Setup

1. **Option 1: MongoDB Atlas (Recommended)**
   - Create free cluster at mongodb.com
   - Get connection string
   - Add to MONGODB_URI in .env

2. **Option 2: Local MongoDB (if supported)**
   - Install MongoDB on server
   - Use: `mongodb://localhost:27017/forexclass`

### Performance Optimization

1. **Enable gzip compression**
2. **Set up PM2 for process management (if available)**
3. **Configure reverse proxy (if available)**

### Security Checklist

- [ ] Change JWT_SECRET to a strong random string
- [ ] Set NODE_ENV=production
- [ ] Enable HTTPS
- [ ] Configure CORS properly
- [ ] Set up rate limiting
- [ ] Regular security updates

### Support

If deployment fails:
1. Check hosting provider's Node.js documentation
2. Contact hosting support for process limits
3. Consider upgrading to VPS/dedicated server
4. Use alternative deployment platforms (Heroku, Railway, DigitalOcean)
