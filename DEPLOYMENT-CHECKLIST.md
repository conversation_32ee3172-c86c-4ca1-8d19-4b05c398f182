# 🚀 ForexClass Deployment Checklist

## ✅ Pre-Deployment (Completed)

- [x] **Frontend Built**: `npm run build` completed successfully
- [x] **Production Package.json**: Created optimized package files
- [x] **Database Seeded**: Packages added to MongoDB
- [x] **Environment Template**: Created .env.production template

## 📁 Files Ready for Upload

### Frontend Files (Upload to public_html/)
```
frontend/dist/
├── index.html
├── vite.svg
└── assets/
    ├── index-S4JYpiG3.css
    └── index-uHf42l_H.js
```

### Backend Files (Upload to Node.js app directory)
```
backend/
├── server.js
├── package-deploy.json (rename to package.json)
├── start-production.js
├── .env.production (rename to .env and update values)
├── models/
├── routes/
├── middleware/
└── seedPackages.js
```

## 🔧 Deployment Steps

### Step 1: Upload Frontend
1. **Upload `frontend/dist/` contents to `public_html/`**
   - index.html → public_html/index.html
   - assets/ → public_html/assets/
   - vite.svg → public_html/vite.svg

### Step 2: Upload Backend
1. **Upload backend files to Node.js app directory**
2. **Rename files:**
   - `package-deploy.json` → `package.json`
   - `.env.production` → `.env`

### Step 3: Configure Environment
1. **Edit .env file with your values:**
   ```env
   NODE_ENV=production
   PORT=3000
   MONGODB_URI=your_mongodb_connection_string
   JWT_SECRET=your-super-secret-jwt-key-change-this
   FRONTEND_URL=https://test.quatromgt.co.ke
   ```

### Step 4: Install Dependencies
**Option A: If npm works**
```bash
npm install --production --no-optional
```

**Option B: If you get fork errors**
```bash
# Install one by one
npm install express --no-optional
npm install mongoose --no-optional
npm install cors --no-optional
npm install helmet --no-optional
npm install bcryptjs --no-optional
npm install jsonwebtoken --no-optional
npm install express-validator --no-optional
npm install express-rate-limit --no-optional
npm install dotenv --no-optional
```

**Option C: Use Yarn**
```bash
yarn install --production --no-optional
```

### Step 5: Configure Node.js App (cPanel)
1. **Set Node.js version**: 18 or latest available
2. **Set startup file**: `server.js` or `start-production.js`
3. **Set environment**: `production`

### Step 6: Database Setup
**Option A: MongoDB Atlas (Recommended)**
1. Create account at mongodb.com
2. Create cluster
3. Get connection string
4. Update MONGODB_URI in .env

**Option B: Seed Database**
```bash
node seedPackages.js
```

## 🧪 Testing Deployment

### Test Backend API
```bash
curl https://test.quatromgt.co.ke:3000/api/health
curl https://test.quatromgt.co.ke:3000/api/packages
```

### Test Frontend
1. Visit: https://test.quatromgt.co.ke
2. Check pricing section loads packages
3. Test admin login at: https://test.quatromgt.co.ke/admin

## 🔍 Troubleshooting

### Common Issues

**1. "fork: Resource temporarily unavailable"**
- Use yarn instead of npm
- Install dependencies one by one
- Contact hosting provider for process limits

**2. "Cannot connect to MongoDB"**
- Check MONGODB_URI in .env
- Ensure MongoDB Atlas IP whitelist includes 0.0.0.0/0
- Test connection string locally first

**3. "Frontend shows blank page"**
- Check browser console for errors
- Ensure all files uploaded correctly
- Check file permissions (644 for files, 755 for directories)

**4. "API endpoints not working"**
- Check Node.js app is running
- Verify PORT in .env matches hosting configuration
- Check server logs for errors

## 📞 Support

If deployment fails:
1. Check hosting provider's Node.js documentation
2. Contact hosting support for:
   - Process/memory limits
   - Node.js version compatibility
   - Port configuration
3. Consider alternative hosting:
   - Railway.app (easy Node.js deployment)
   - Heroku
   - DigitalOcean App Platform
   - Vercel (frontend) + Railway (backend)

## 🎉 Success Indicators

- ✅ Frontend loads at your domain
- ✅ Pricing section shows 3 packages (Basic, Premium, VIP)
- ✅ Admin login works
- ✅ Package management in admin works
- ✅ Changes in admin reflect on frontend
