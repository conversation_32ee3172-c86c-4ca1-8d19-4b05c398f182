const mongoose = require('mongoose');
const Package = require('./models/Package');
require('dotenv').config();

const seedPackages = async () => {
  try {
    // Connect to MongoDB
    await mongoose.connect(process.env.MONGODB_URI || 'mongodb://localhost:27017/forexclass');
    console.log('✅ Connected to MongoDB');

    // Clear existing packages
    await Package.deleteMany({});
    console.log('🗑️ Cleared existing packages');

    // Create default packages
    const packages = [
      {
        name: 'Basic',
        description: 'Perfect for beginners starting their trading journey',
        price: 29,
        duration: 30,
        features: [
          'Access to basic courses',
          'Community forum access',
          'Email support',
          'Basic trading guides',
          'Mobile app access'
        ],
        isActive: true,
        popular: false,
        color: 'gray',
        displayOrder: 1
      },
      {
        name: 'Premium',
        description: 'Most popular choice for serious traders',
        price: 99,
        duration: 30,
        features: [
          'Access to all courses',
          'Premium Telegram signals',
          '1-on-1 mentoring sessions',
          'Advanced trading strategies',
          'Priority support',
          'Market analysis reports',
          'Live trading sessions',
          'Risk management tools'
        ],
        isActive: true,
        popular: true,
        color: 'blue',
        displayOrder: 2
      },
      {
        name: 'VIP',
        description: 'For professional traders seeking maximum results',
        price: 199,
        duration: 30,
        features: [
          'Everything in Premium',
          'Personal trading coach',
          'Custom trading strategies',
          'Direct access to expert traders',
          'Weekly live sessions',
          'Portfolio review',
          'Exclusive market insights',
          'Priority Telegram channel'
        ],
        isActive: true,
        popular: false,
        color: 'purple',
        displayOrder: 3
      }
    ];

    // Insert packages
    const createdPackages = await Package.insertMany(packages);
    console.log(`✅ Created ${createdPackages.length} packages:`);
    
    createdPackages.forEach(pkg => {
      console.log(`   - ${pkg.name}: $${pkg.price}/month (${pkg.features.length} features)`);
    });

    console.log('\n🎉 Package seeding completed successfully!');
    process.exit(0);

  } catch (error) {
    console.error('❌ Error seeding packages:', error);
    process.exit(1);
  }
};

// Run the seeder
seedPackages();
