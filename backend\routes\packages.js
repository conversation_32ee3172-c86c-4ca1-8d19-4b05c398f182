const express = require('express');
const { body, validationResult } = require('express-validator');
const Package = require('../models/Package');
const auth = require('../middleware/auth');
const adminAuth = require('../middleware/adminAuth');

const router = express.Router();

// @route   GET /api/packages
// @desc    Get all active packages (Public)
// @access  Public
router.get('/', async (req, res) => {
  try {
    const packages = await Package.getActivePackages();

    res.json({
      success: true,
      data: {
        packages
      }
    });

  } catch (error) {
    console.error('Get packages error:', error);
    res.status(500).json({
      success: false,
      message: 'Server error'
    });
  }
});

// @route   GET /api/packages/admin
// @desc    Get all packages for admin (including inactive)
// @access  Private/Admin
router.get('/admin', auth, adminAuth, async (req, res) => {
  try {
    const packages = await Package.getAllPackagesForAdmin();

    res.json({
      success: true,
      data: {
        packages
      }
    });

  } catch (error) {
    console.error('Get admin packages error:', error);
    res.status(500).json({
      success: false,
      message: 'Server error'
    });
  }
});

// @route   POST /api/packages
// @desc    Create new package (Admin only)
// @access  Private/Admin
router.post('/', auth, adminAuth, [
  body('name').trim().isLength({ min: 2, max: 50 }).withMessage('Name must be 2-50 characters'),
  body('price').isFloat({ min: 0 }).withMessage('Price must be a positive number'),
  body('duration').isInt({ min: 1 }).withMessage('Duration must be at least 1 day'),
  body('features').isArray({ min: 1 }).withMessage('At least one feature is required'),
  body('description').optional().isLength({ max: 200 }).withMessage('Description cannot exceed 200 characters')
], async (req, res) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        success: false,
        message: 'Validation failed',
        errors: errors.array()
      });
    }

    const { name, description, price, duration, features, isActive, popular, color, displayOrder } = req.body;

    // Check if package with same name exists
    const existingPackage = await Package.findOne({ name: { $regex: new RegExp(`^${name}$`, 'i') } });
    if (existingPackage) {
      return res.status(400).json({
        success: false,
        message: 'Package with this name already exists'
      });
    }

    const package = new Package({
      name,
      description,
      price,
      duration,
      features: features.filter(f => f.trim()),
      isActive: isActive !== undefined ? isActive : true,
      popular: popular || false,
      color: color || 'blue',
      displayOrder: displayOrder || 0
    });

    await package.save();

    res.status(201).json({
      success: true,
      message: 'Package created successfully',
      data: {
        package
      }
    });

  } catch (error) {
    console.error('Create package error:', error);
    res.status(500).json({
      success: false,
      message: 'Server error'
    });
  }
});

// @route   PUT /api/packages/:id
// @desc    Update package (Admin only)
// @access  Private/Admin
router.put('/:id', auth, adminAuth, [
  body('name').optional().trim().isLength({ min: 2, max: 50 }).withMessage('Name must be 2-50 characters'),
  body('price').optional().isFloat({ min: 0 }).withMessage('Price must be a positive number'),
  body('duration').optional().isInt({ min: 1 }).withMessage('Duration must be at least 1 day'),
  body('features').optional().isArray({ min: 1 }).withMessage('At least one feature is required'),
  body('description').optional().isLength({ max: 200 }).withMessage('Description cannot exceed 200 characters')
], async (req, res) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        success: false,
        message: 'Validation failed',
        errors: errors.array()
      });
    }

    const package = await Package.findById(req.params.id);
    if (!package) {
      return res.status(404).json({
        success: false,
        message: 'Package not found'
      });
    }

    const { name, description, price, duration, features, isActive, popular, color, displayOrder } = req.body;

    // Check if another package with same name exists
    if (name && name !== package.name) {
      const existingPackage = await Package.findOne({
        name: { $regex: new RegExp(`^${name}$`, 'i') },
        _id: { $ne: req.params.id }
      });
      if (existingPackage) {
        return res.status(400).json({
          success: false,
          message: 'Package with this name already exists'
        });
      }
    }

    // Update fields
    if (name !== undefined) package.name = name;
    if (description !== undefined) package.description = description;
    if (price !== undefined) package.price = price;
    if (duration !== undefined) package.duration = duration;
    if (features !== undefined) package.features = features.filter(f => f.trim());
    if (isActive !== undefined) package.isActive = isActive;
    if (popular !== undefined) package.popular = popular;
    if (color !== undefined) package.color = color;
    if (displayOrder !== undefined) package.displayOrder = displayOrder;

    await package.save();

    res.json({
      success: true,
      message: 'Package updated successfully',
      data: {
        package
      }
    });

  } catch (error) {
    console.error('Update package error:', error);
    res.status(500).json({
      success: false,
      message: 'Server error'
    });
  }
});

// @route   DELETE /api/packages/:id
// @desc    Delete package (Admin only)
// @access  Private/Admin
router.delete('/:id', auth, adminAuth, async (req, res) => {
  try {
    const package = await Package.findById(req.params.id);

    if (!package) {
      return res.status(404).json({
        success: false,
        message: 'Package not found'
      });
    }

    // Check if package has active subscribers
    if (package.subscribers > 0) {
      return res.status(400).json({
        success: false,
        message: 'Cannot delete package with active subscribers. Deactivate it instead.'
      });
    }

    await Package.findByIdAndDelete(req.params.id);

    res.json({
      success: true,
      message: 'Package deleted successfully'
    });

  } catch (error) {
    console.error('Delete package error:', error);
    res.status(500).json({
      success: false,
      message: 'Server error'
    });
  }
});

module.exports = router;
