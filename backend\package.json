{"name": "forexclass-backend", "version": "1.0.0", "description": "ForexClass Trading Platform Backend API", "main": "server.js", "scripts": {"start": "node server.js", "dev": "nodemon server.js", "build": "echo 'No build step required for backend'", "postinstall": "echo 'Backend dependencies installed'", "test": "echo \"Error: no test specified\" && exit 1"}, "keywords": ["forex", "trading", "education", "api", "mongodb", "express"], "author": "ForexClass Team", "license": "MIT", "dependencies": {"bcryptjs": "^2.4.3", "cors": "^2.8.5", "dotenv": "^16.3.1", "express": "^4.18.2", "express-rate-limit": "^7.1.5", "express-validator": "^7.0.1", "helmet": "^7.1.0", "jsonwebtoken": "^9.0.2", "mongoose": "^8.0.3"}, "devDependencies": {"nodemon": "^3.0.2"}, "engines": {"node": ">=16.0.0"}}